using AwhGameServer.Domain.Abstractions;
using AwhGameServer.Domain.Exceptions;
using AwhGameServer.Domain.ValueObjects.Ids.Users;

namespace AwhGameServer.Domain.Entities.Users;

/// <summary>
/// Точка входа в пользовательский аккаунт.
/// </summary>
/// 
/// <remarks>
/// Каждая точка входа определяет уникальный способ аутентификации пользователя 
/// (например, GuestId, OAuth2 Google, OAuth2 Apple, социальные сети).
/// Один аккаунт может иметь несколько таких точек входа, что позволяет, например,
/// начать игру как гость и позже привязать аккаунт к платформе, так же открывает возможность
/// играть с одним аккаунтом на разных платформах (Apple/Google).
///
/// Объект хранит:
/// <list type="bullet">
/// <item><description><see cref="AuthIdentityId"/> — идентификатор точки входа.</description></item>
/// <item><description><see cref="UserId"/> — идентификатор владельца аккаунта.</description></item>
/// <item><description><see cref="AuthMethod"/> — ключ метода аутентификации.</description></item>
/// <item><description><see cref="AuthToken"/> — токен или уникальный идентификатор в рамках данного метода.</description></item>
/// </list>
/// </remarks>
public class AuthIdentity(AuthIdentityId id, UserId userId, string authToken, string authMethod) : IAggregateRoot, IEntity
{
    public AuthIdentityId Id { get; } = id ?? throw new DomainException("Id cannot be null");
    public UserId UserId { get; } = userId ?? throw new DomainException("UserId cannot be null");
    public string AuthMethod { get; } = string.IsNullOrWhiteSpace(authMethod) 
        ? throw new DomainException("Auth method cannot be null or empty") 
        : authMethod;

    public string AuthToken { get; } = string.IsNullOrWhiteSpace(authToken) 
        ? throw new DomainException("Auth token cannot be null or empty") 
        : authToken;
}
