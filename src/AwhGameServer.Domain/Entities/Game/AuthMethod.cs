using AwhGameServer.Domain.Abstractions;
using AwhGameServer.Domain.Exceptions;

namespace AwhGameServer.Domain.Entities.Game;

/// <summary>
/// Способ аутентификации пользователей.
/// </summary>
/// 
/// <param name="methodKey">
/// Уникальный идентификатор способа аутентификации (чувствителен к регистру).
/// Примеры: OAuth2.0_Google, OAuth2.0_Apple, OAuth2.0_VK, PhoneNumber, EmailPassword.
/// Не может быть пустым или состоять только из пробелов.
/// </param>
/// 
/// <param name="isRegistrationAllowed">Разрешена ли регистрация через данный метод.</param>
/// <param name="isLoginAllowed">Разрешён ли вход через данный метод.</param>
public class AuthMethod(string methodKey, bool isRegistrationAllowed, bool isLoginAllowed) : IEntity
{
    /// <summary>
    /// Уникальный идентификатор способа аутентификации (чувствителен к регистру).
    /// Примеры: OAuth2.0_Google, OAuth2.0_Apple, OAuth2.0_VK, PhoneNumber, EmailPassword.
    /// </summary>
    public string MethodKey { get; } = 
        string.IsNullOrWhiteSpace(methodKey) 
            ? throw new DomainException("Method key cannot be null or empty") 
            : methodKey;

    /// <summary>
    /// Разрешена ли регистрация через данный метод.
    /// Запрет регистрации не препятствует входу, если он разрешён.
    /// </summary>
    public bool IsRegistrationAllowed => IsLoginAllowed && isRegistrationAllowed;

    /// <summary>
    /// Разрешён ли вход через данный метод.
    /// Запрет входа также блокирует регистрацию.
    /// </summary>
    public bool IsLoginAllowed { get; } = isLoginAllowed;
}
