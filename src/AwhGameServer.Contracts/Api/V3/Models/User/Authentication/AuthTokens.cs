namespace AwhGameServer.Contracts.Api.V3.Models.User.Authentication;

/// <summary>
/// Контрактная модель токенов аутентификации.
/// Содержит пару токенов для доступа к API и обновления сессии.
/// </summary>
public class AuthTokens
{
    /// <summary>
    /// Токен доступа для авторизации API запросов.
    /// Имеет ограниченное время жизни и используется для аутентификации пользователя.
    /// </summary>
    public string AccessToken { get; set; }
    
    /// <summary>
    /// Время истечения токена доступа в UTC.
    /// </summary>
    public DateTime AccessTokenExpiresAtUtc { get; set; }

    /// <summary>
    /// Токен обновления для получения новых токенов доступа.
    /// Имеет более длительное время жизни и используется для обновления сессии.
    /// </summary>
    public string RefreshToken { get; set; }
    
    /// <summary>
    /// Время истечения токена обновления в UTC.
    /// </summary>
    public DateTime RefreshTokenExpiresAtUtc { get; set; }
}
