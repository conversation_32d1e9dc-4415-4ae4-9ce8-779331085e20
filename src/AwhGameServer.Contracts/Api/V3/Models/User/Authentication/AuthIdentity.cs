namespace AwhGameServer.Contracts.Api.V3.Models.User.Authentication;

/// <summary>
/// Контрактная модель идентификационных данных для аутентификации пользователя.
/// Представляет способ аутентификации и соответствующий токен.
/// Является точкой входа в аккаунт пользователя.
/// </summary>
public class AuthIdentity
{
    /// <summary>
    /// Метод аутентификации.
    /// Примеры: "Guest", "OAuth2.0_GooglePlayGames", "OAuth2.0_AppleGameCenter".
    /// </summary>
    public string AuthMethod { get; set; }

    /// <summary>
    /// Токен аутентификации для указанного метода.
    /// </summary>
    public string AuthToken { get; set; }
}
