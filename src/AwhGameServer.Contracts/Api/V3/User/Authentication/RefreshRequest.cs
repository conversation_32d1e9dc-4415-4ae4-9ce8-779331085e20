namespace AwhGameServer.Contracts.Api.V3.User.Authentication;

/// <summary>
/// Контрактная модель запроса на обновление токенов доступа.
/// Используется для получения новых токенов без повторной аутентификации.
/// </summary>
public class RefreshRequest
{
    /// <summary>
    /// Адрес эндпоинта для обновления токенов доступа.
    /// </summary>
    public const string EndPointPath = "/api/v3/user/authentication/refresh";
    
    /// <summary>
    /// Токен обновления для получения новых токенов доступа.
    /// Должен быть действительным и не истекшим.
    /// </summary>
    public string RefreshToken { get; set; }
}
