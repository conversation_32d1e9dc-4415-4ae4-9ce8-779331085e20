using AwhGameServer.Contracts.Api.V3.Models.User.Authentication;

namespace AwhGameServer.Contracts.Api.V3.User.Authentication;

/// <summary>
/// Контрактная модель запроса на вход в систему.
/// Содержит идентификационные данные пользователя для аутентификации.
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Адрес эндпоинта для входа в систему.
    /// </summary>
    public const string EndPointPath = "/api/v3/user/authentication/login";
    
    /// <summary>
    /// Массив идентификационных данных для аутентификации.
    /// Пользователь может предоставить гостевой и не-гостевой токены одновременно.
    /// Приоритет будет иметь не-гостевой токен.
    /// Массив не должен быть пустым.
    /// </summary>
    public AuthIdentity[] AuthIdentities { get; set; }
}
