using AwhGameServer.Contracts.Api.V3.Models.User.Authentication;

namespace AwhGameServer.Contracts.Api.V3.User.Authentication;

/// <summary>
/// Контрактная модель ответа на запрос входа в систему.
/// Содержит токены аутентификации для успешно аутентифицированного пользователя.
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Идентификатор пользователя.
    /// </summary>
    public string UserId { get; set; }
    
    /// <summary>
    /// Флаг, указывающий, что пользователь является новым.
    /// </summary>
    public bool IsNewUser { get; set; }
    
    /// <summary>
    /// Токены аутентификации, выданные пользователю после успешного входа.
    /// Включают токен доступа и токен обновления.
    /// </summary>
    public AuthTokens AuthTokens { get; set; }
}
