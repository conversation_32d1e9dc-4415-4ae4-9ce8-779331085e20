using Microsoft.EntityFrameworkCore;
using AwhGameServer.Domain.Entities.Game;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.GameData;
using AwhGameServer.Infrastructure.Persistence.Ef.ModelsConfigurations.GameData;

namespace AwhGameServer.Infrastructure.Persistence.Ef;

/// <summary>
/// Контекст Entity Framework для базы игровых данных.
/// Предоставляет доступ к статическим данным игры, таким как методы аутентификации.
/// Использует MongoDB в качестве провайдера базы данных.
/// </summary>
public sealed class GameDataDbContext : DbContext
{
    /// <summary>
    /// Набор методов аутентификации, доступных в системе.
    /// Содержит информацию о том, какие способы входа и регистрации разрешены.
    /// Представляет собой модель хранения <see cref="AuthMethod"/> из домена.
    /// </summary>
    public DbSet<AuthMethodDocument> AuthMethods { get; set; } = null!;

    /// <summary>
    /// Инициализирует новый экземпляр контекста базы игровых данных.
    /// Он пустой, так как Entity Framework требует конструктор с параметром options.
    /// </summary>
    public GameDataDbContext(DbContextOptions<GameDataDbContext> options) : base(options)
    {

    }

    /// <summary>
    /// Настраивает модель данных при создании контекста.
    /// Применяет конфигурации Entity Framework для всех сущностей.
    /// </summary>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new AuthMethodDocumentConfiguration());

        base.OnModelCreating(modelBuilder);
    }
}
