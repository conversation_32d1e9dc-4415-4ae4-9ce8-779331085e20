using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация сессий аутентификации.
/// Содержит параметры для генерации access и refresh токенов.
/// </summary>
public class AuthSessionConfig
{
    /// <summary>
    /// Издатель токена доступа (issuer).
    /// </summary>
    [Required(ErrorMessage = "AccessTokenIssuer is required")]
    public string AccessTokenIssuer { get; set; } = string.Empty;
    
    /// <summary>
    /// Аудитория токена доступа (audience).
    /// </summary>
    [Required(ErrorMessage = "AccessTokenAudience is required")]
    public string AccessTokenAudience { get; set; } = string.Empty;
    
    /// <summary>
    /// Время жизни токена доступа в минутах.
    /// </summary>
    [Required(ErrorMessage = "AccessTokenLifetimeMinutes is required")]
    public int AccessTokenLifetimeMinutes { get; set; } = 15;
    
    /// <summary>
    /// Секретный ключ для подписи токена доступа в формате Base64.
    /// </summary>
    [Required(ErrorMessage = "AccessTokenSecretBase64 is required")]
    [MinLength(64, ErrorMessage = "AccessTokenSecretBase64 must be at least 64 characters long")]
    public string AccessTokenSecretBase64 { get; set; } = string.Empty;
    
    /// <summary>
    /// Время жизни refresh токена в днях.
    /// </summary>
    [Required(ErrorMessage = "RefreshTokenLifetimeDays is required")]
    public int RefreshTokenLifetimeDays { get; set; } = 30;
}
