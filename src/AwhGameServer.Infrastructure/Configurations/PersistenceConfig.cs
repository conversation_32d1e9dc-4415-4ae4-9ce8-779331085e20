using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Основная конфигурация слоя персистентности.
/// Содержит настройки подключения к MongoDB и конфигурации отдельных баз данных.
/// </summary>
public class PersistenceConfig
{
    /// <summary>
    /// Строка подключения к MongoDB.
    /// Используется для создания клиента MongoDB, который затем применяется
    /// для всех MongoDB баз данных в системе.
    /// </summary>
    [Required(ErrorMessage = "MongoDbConnectionString is required")]
    public string MongoDbConnectionString { get; set; } = null!;

    /// <summary>
    /// Конфигурация базы игровых данных, содержащей конфигурационные данные игры.
    /// </summary>
    public GameDataDbConfig GameDataDb { get; set; } = null!;

    /// <summary>
    /// Конфигурация базы пользовательских данных.
    /// </summary>
    public UsersDataDbConfig UsersDataDb { get; set; } = null!;
}
