using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация генератора идентификаторов пользователей.
/// Определяет параметры для создания структурированных ID в формате SGNNNNNNNN.
/// </summary>
public class UserIdGeneratorConfig
{
    /// <summary>
    /// Код сервера (1 цифра, от 1 до 9).
    /// Используется как первая цифра в идентификаторе пользователя.
    /// Позволяет различать пользователей разных серверов.
    /// </summary>
    [Required(ErrorMessage = "ServerCode is required")]
    [Range(1, 9, ErrorMessage = "ServerCode must be between 1 and 9")]
    public int ServerCode { get; set; }

    /// <summary>
    /// Поколение пользователей (1 цифра, от 0 до 9).
    /// Используется как вторая цифра в идентификаторе пользователя.
    /// Позволяет различать разные поколения пользователей на одном сервере.
    /// </summary>
    [Required(ErrorMessage = "UserGeneration is required")]
    [Range(0, 9, ErrorMessage = "UserGeneration must be between 0 and 9")]
    public int UserGeneration { get; set; }

    /// <summary>
    /// Ключ счетчика в базе данных.
    /// Используется для идентификации конкретного счетчика в коллекции counters.
    /// Должен быть уникальным для каждой комбинации сервера и поколения.
    /// </summary>
    [Required(ErrorMessage = "CounterKey is required")]
    public string CounterKey { get; set; } = null!;
}
