using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация базы пользовательских данных.
/// </summary>
public class UsersDataDbConfig
{
    /// <summary>
    /// Имя базы данных MongoDB для хранения пользовательских данных.
    /// </summary>
    [Required(ErrorMessage = "DatabaseName is required")]
    public string DatabaseName { get; set; } = null!;
}
