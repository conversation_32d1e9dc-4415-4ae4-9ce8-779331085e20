using AwhGameServer.Contracts.Api.V3.Models.User.Authentication;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models.User.Authentication;

/// <summary>
/// Маппер для преобразования между контрактными и доменными моделями токенов аутентификации.
/// </summary>
public static class AuthTokensMapper
{
    /// <summary>
    /// Преобразует DTO токенов аутентификации уровня приложения в контрактную модель.
    /// </summary>
    /// 
    /// <param name="authTokensDto">DTO токенов аутентификации уровня приложения</param>
    /// 
    /// <returns>Контрактная модель токенов аутентификации</returns>
    public static AuthTokens MapToContract(this Application.Dto.AuthTokensDto authTokensDto)
    {
        return new AuthTokens
        {
            AccessToken = authTokensDto.AccessToken,
            AccessTokenExpiresAtUtc = authTokensDto.AccessTokenExpiresAtUtc,
            RefreshToken = authTokensDto.RefreshToken,
            RefreshTokenExpiresAtUtc = authTokensDto.RefreshTokenExpiresAtUtc
        };
    }
}
