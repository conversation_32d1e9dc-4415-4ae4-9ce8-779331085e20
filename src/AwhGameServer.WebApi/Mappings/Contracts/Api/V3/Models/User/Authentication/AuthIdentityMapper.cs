using AwhGameServer.Contracts.Api.V3.Models.User.Authentication;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models.User.Authentication;

/// <summary>
/// Маппер для преобразования между контрактными и доменными моделями идентификационных данных.
/// </summary>
public static class AuthIdentityMapper
{
    /// <summary>
    /// Преобразует контрактную модель идентификационных данных в DTO уровня приложения.
    /// </summary>
    /// 
    /// <param name="authIdentity">Контрактная модель идентификационных данных</param>
    /// 
    /// <returns>DTO идентификационных данных для уровня приложения</returns>
    public static Application.Dto.AuthIdentityDto MapToApplicationDto(this AuthIdentity authIdentity)
    {
        return new Application.Dto.AuthIdentityDto(authIdentity.AuthMethod, authIdentity.AuthToken);
    }

    /// <summary>
    /// Преобразует DTO идентификационных данных уровня приложения в контрактную модель.
    /// </summary>
    /// 
    /// <param name="authIdentityDto">DTO идентификационных данных уровня приложения</param>
    /// 
    /// <returns>Контрактная модель идентификационных данных</returns>
    public static AuthIdentity MapToContract(this Application.Dto.AuthIdentityDto authIdentityDto)
    {
        return new AuthIdentity
        {
            AuthMethod = authIdentityDto.AuthMethod,
            AuthToken = authIdentityDto.AuthToken
        };
    }
}
