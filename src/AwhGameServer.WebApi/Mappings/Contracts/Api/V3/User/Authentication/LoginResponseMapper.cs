using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.User.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models.User.Authentication;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.User.Authentication;

/// <summary>
/// Маппер для преобразования результатов входа из уровня приложения в контрактные модели.
/// </summary>
public static class LoginResponseMapper
{
    /// <summary>
    /// Преобразует результат команды входа уровня приложения в контрактный ответ.
    /// </summary>
    /// 
    /// <param name="loginCommandResult">Результат команды входа уровня приложения</param>
    /// 
    /// <returns>Контрактный ответ на запрос входа</returns>
    public static LoginResponse MapToContract(this LoginCommandResult loginCommandResult)
    {
        return new LoginResponse
        {
            UserId = loginCommandResult.UserId,
            IsNewUser = loginCommandResult.IsNewUser,
            AuthTokens = loginCommandResult.AuthTokens.MapToContract()
        };
    }
}
