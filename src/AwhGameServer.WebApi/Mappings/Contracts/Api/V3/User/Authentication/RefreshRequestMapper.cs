using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.User.Authentication;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.User.Authentication;

/// <summary>
/// Маппер для преобразования запросов на обновление токенов из контрактной модели в команды уровня приложения.
/// </summary>
public static class RefreshRequestMapper
{
    /// <summary>
    /// Преобразует контрактный запрос на обновление токенов в команду уровня приложения.
    /// </summary>
    /// 
    /// <param name="refreshRequest">Контрактный запрос на обновление токенов</param>
    /// 
    /// <returns>Команда обновления токенов для уровня приложения</returns>
    public static RefreshCommand MapToApplicationCommand(this RefreshRequest refreshRequest)
    {
        return new RefreshCommand(refreshRequest.RefreshToken);
    }
}
