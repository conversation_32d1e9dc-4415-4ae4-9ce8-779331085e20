using Microsoft.Extensions.Options;
using Serilog;
using AwhGameServer.WebApi.Middleware;

namespace AwhGameServer.WebApi.Extensions;

/// <summary>
/// Расширения для настройки конвейера обработки HTTP-запросов в WebApi.
/// Предоставляет централизованную точку для настройки всех компонентов WebApi.
/// </summary>
public static class WebApiWebApplicationExtensions
{
    /// <summary>
    /// Запускает приложение, обрабатывающее HTTP-запросы.
    /// </summary>
    public static WebApplication RunWebApi(this WebApplication app)
    {
        try
        {
            app.Run();
        }
        catch (OptionsValidationException ex)
        {
            Log.Fatal("Hosting failed to start.");
            
            LogFatalOptionsValidation(ex);
            
            Environment.ExitCode = 1;
        }
        catch (AggregateException agg) when (agg.InnerExceptions.All(e => e is OptionsValidationException))
        {
            Log.Fatal("Hosting failed to start.");

            foreach (var ex in agg.InnerExceptions.Cast<OptionsValidationException>())
            {
                LogFatalOptionsValidation(ex);
            }
            
            Environment.ExitCode = 1;
        }
        
        return app;
        
        void LogFatalOptionsValidation(OptionsValidationException ex)
        {
            Log.Fatal("Config validations failed for {OptionsType}.", ex.OptionsType.Name);

            foreach (var failure in ex.Failures)
            {
                Log.Fatal(failure);
            }
        }
    }
    
    /// <summary>
    /// Настраивает конвейер обработки HTTP-запросов для WebApi.
    /// </summary>
    public static WebApplication UseWebApi(this WebApplication app)
    {
        app.UseLogging();
        
        app.MapControllers();
        
        return app;
    }
    
    /// <summary>
    /// Добавляет логирование в конвейер обработки запросов.
    /// </summary>
    private static WebApplication UseLogging(this WebApplication app)
    {
        app.UseMiddleware<UserEnricherMiddleware>(); 
        app.UseSerilogRequestLogging();
        
        return app;
    }
}
