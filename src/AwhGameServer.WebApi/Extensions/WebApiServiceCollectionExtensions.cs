using System.Reflection;
using Microsoft.Extensions.Options;
using FluentValidation;
using SharpGrip.FluentValidation.AutoValidation.Mvc.Extensions;
using Serilog;

namespace AwhGameServer.WebApi.Extensions;

/// <summary>
/// Расширения для регистрации сервисов уровня WebApi в контейнере зависимостей.
/// Предоставляет централизованную точку для настройки всех компонентов WebApi.
/// </summary>
public static class WebApiServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы WebApi в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddWebApi(
        this IServiceCollection services, 
        IConfiguration configuration,
        IHostBuilder host)
    {
        services.AddLogging(configuration, host);
        
        services.AddControllers();

        services.AddRequestValidations();

        return services;
    }
    
    /// <summary>
    /// Регистрирует логирование в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddLogging(
        this IServiceCollection services,
        IConfiguration configuration,
        IHostBuilder host)
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.FromLogContext()
            .Filter.ByExcluding(le => ShouldSuppressStartupException(le.Exception))
            .CreateLogger();

        host.UseSerilog();
        
        return services;
        
        static bool ShouldSuppressStartupException(Exception? ex)
        {
            HashSet<Type> excludedTypes = [typeof(OptionsValidationException)];
            
            if (ex == null) return false;

            // Если это один из исключаемых типов — скрываем
            if (excludedTypes.Any(t => t.IsInstanceOfType(ex)))
                return true;

            // Пробуем «развернуть» одиночные обёртки (TargetInvocationException, TypeInitializationException и т.п.)
            if (ex is not AggregateException agg)
                return ex.InnerException != null && ShouldSuppressStartupException(ex.InnerException);
            
            // Если это Aggregate — все внутренние должны быть исключаемыми
            var flat = agg.Flatten();
            return flat.InnerExceptions.Count > 0 && flat.InnerExceptions.All(ShouldSuppressStartupException);
        }
    }
    
    /// <summary>
    /// Регистрирует валидаторы запросов в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddRequestValidations(this IServiceCollection services)
    {
        services.AddValidatorsFromAssembly(Assembly.GetCallingAssembly());
        services.AddFluentValidationAutoValidation();

        return services;
    }
}
