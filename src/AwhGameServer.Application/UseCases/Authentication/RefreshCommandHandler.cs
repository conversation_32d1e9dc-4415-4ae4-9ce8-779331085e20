using AwhGameServer.Domain.Entities.Users;
using AwhGameServer.Application.Abstractions.Messaging;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Application.Abstractions.Logging;
using AwhGameServer.Application.Dto;
using AwhGameServer.Application.Exceptions;

namespace AwhGameServer.Application.UseCases.Authentication;

/// <summary>
/// Обработчик команды обновления токенов авторизации (refresh token flow).
/// </summary>
/// 
/// <remarks>
/// Реализует процесс получения новой пары access/refresh токенов при действительной сессии пользователя.
/// 
/// Логика работы:
/// <list type="number">
/// <item>Проверяет, что refresh token не пустой.</item>
/// <item>Вычисляет хэш refresh token и ищет связанную сессию в <see cref="IAuthSessionStore"/>.</item>
/// <item>Проверяет, что сессия существует, не отозвана и не истекла.</item>
/// <item>Загружает точку входа (<see cref="AuthIdentity"/>) и проверяет, что метод аутентификации разрешён для входа.</item>
/// <item>Генерирует новую пару токенов и продлевает сессию (ротация refresh token).</item>
/// </list>
/// </remarks>
public class RefreshCommandHandler(
    IAuthenticationUow uow,
    IAuthTokensGenerator authTokensGenerator,
    ITokenHasher tokenHasher,
    IAuthSessionStore authSessionStore,
    IAppLogger<RefreshCommandHandler> logger)
    : ICommandHandler<RefreshCommand, RefreshCommandResult>
{
    public async Task<RefreshCommandResult> Handle(RefreshCommand command, CancellationToken ct)
    {
        using var scope = logger.BeginScope("RefreshCommand");
        logger.Info("Начало обработки команды обновления токенов");
        
        if (string.IsNullOrWhiteSpace(command.RefreshToken))
        {
            logger.Warn("Попытка обновления с пустым refresh token");
            throw new ApplicationArgumentException("Refresh token cannot be null or empty", nameof(command.RefreshToken));
        }

        logger.Debug("Хеширование refresh token для поиска сессии");
        var refreshTokenHash = await tokenHasher.HashToken(command.RefreshToken, ct);

        logger.Debug("Поиск сессии по хешу refresh token");
        var session = await authSessionStore.GetSessionByRefreshHashAsync(refreshTokenHash, ct);

        if (session is null)
        {
            logger.Warn("Сессия не найдена по предоставленному refresh token");
            throw new AuthenticationException("Refresh token is invalid or expired");
        }

        logger.Debug("Найдена сессия {SessionId}, проверка статуса", session.SessionId);

        if (session.IsRevoked)
        {
            logger.Warn("Попытка использования отозванного refresh token для сессии {SessionId}", session.SessionId);
            throw new AuthenticationException("Refresh token is revoked");
        }

        if (session.ExpiresAtUtc < DateTime.UtcNow)
        {
            logger.Warn("Попытка использования истекшего refresh token для сессии {SessionId}, истек {ExpiresAt}",
                session.SessionId, session.ExpiresAtUtc);
            throw new AuthenticationException("Refresh token is expired");
        }

        logger.Debug("Загрузка точки входа {AuthIdentityId}", session.AuthIdentityId.Value);
        var authIdentity = await uow.AuthIdentityRepository.GetByIdAsync(session.AuthIdentityId, ct);

        if (authIdentity is null)
        {
            var exception = new AuthenticationException("Auth identity not found");
            logger.Error(exception, "Точка входа {AuthIdentityId} не найдена для сессии {SessionId}",
                session.AuthIdentityId.Value, session.SessionId);
            throw exception;
        }

        logger.Debug("Проверка разрешения входа для метода {AuthMethod}", authIdentity.AuthMethod);
        var authMethodsConfig = await uow.AuthMethodsConfigRepository.GetConfigAsync(ct);

        if (!authMethodsConfig.IsLoginMethodAllowed(authIdentity.AuthMethod))
        {
            logger.Warn("Метод аутентификации {AuthMethod} больше не разрешен для входа", authIdentity.AuthMethod);
            throw new AuthenticationException($"Login with {authIdentity.AuthMethod} is not allowed");
        }

        logger.Debug("Генерация новых токенов для сессии {SessionId}", session.SessionId);
        var authTokens = await authTokensGenerator.GenerateAuthTokens(session.SessionId, authIdentity.UserId, ct);

        logger.Debug("Хеширование нового refresh token");
        var newRefreshTokenHash = await tokenHasher.HashToken(authTokens.RefreshToken, ct);

        var newSessionExpiresAtUtc = authTokens.RefreshTokenExpiresAtUtc;

        logger.Info("Ротация refresh token для сессии {SessionId}, новое время истечения {ExpiresAt}",
            session.SessionId, newSessionExpiresAtUtc);
        var result = await authSessionStore.TryRotateRefreshAsync(
            refreshTokenHash,
            newRefreshTokenHash,
            newSessionExpiresAtUtc, // Продлеваем сессию на то же время, что и refresh token
            ct);

        if (!result)
        {
            logger.Warn("Не удалось выполнить ротацию refresh token для сессии {SessionId}", session.SessionId);
            throw new AuthenticationException("Refresh token is invalid or expired");
        }

        var authTokensDto = new AuthTokensDto(
            authTokens.AccessToken,
            authTokens.AccessTokenExpiresAtUtc,
            authTokens.RefreshToken,
            authTokens.RefreshTokenExpiresAtUtc);

        logger.Info("Успешное обновление токенов для пользователя {UserId}, сессия {SessionId}",
            authIdentity.UserId.Value, session.SessionId);

        return new RefreshCommandResult(
            authIdentity.UserId.Value,
            session.SessionId,
            newSessionExpiresAtUtc,
            authTokensDto);
    }
}
