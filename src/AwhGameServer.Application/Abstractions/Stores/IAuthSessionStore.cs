using AwhGameServer.Domain.ValueObjects.Ids.Users;
using AwhGameServer.Application.Models;

namespace AwhGameServer.Application.Abstractions.Stores;

/// <summary>
/// Хранилище сессий аутентификации.
/// </summary>
public interface IAuthSessionStore
{
    /// <summary>
    /// Получить сессию по идентификатору сессии.
    /// </summary>
    ///
    /// <returns>Возвращает сессию, если найдена и не истекла, иначе <see langword="null"/>.</returns>
    public Task<AuthSession?> GetSessionAsync(
        string sessionId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Получить сессию по хешу refresh токена.
    /// </summary>
    ///
    /// <returns>Возвращает сессию, если найдена и не истекла, иначе <see langword="null"/>.</returns>
    public Task<AuthSession?> GetSessionByRefreshHashAsync(
        TokenHash refreshTokenHash,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Атомарная операция создания новой сессии, с предварительным аннулированием всех предыдущих сессий для пользователя.
    /// Аннулирование предыдущих сессий означает, что они считаются завершёнными и не могут быть использованы для аутентификации.
    /// Аннулирование не означает удаление сессий из хранилища, так как это может быть полезно для аудита.
    /// </summary>
    public Task RevokeAllUserSessionsThenCreateAsync(
        string sessionId,
        UserId userId,
        TokenHash refreshTokenHash,
        AuthIdentityId authIdentityId,
        DateTime expiresAtUtc,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Атомарная операция обновления хеша refresh токена.
    /// Обновление хеша refresh токена означает, что предыдущий refresh токен больше не может быть использован для аутентификации.
    /// </summary>
    /// 
    /// <returns>Возвращает true, если сессия найдена и обновлена, false в противном случае.</returns>
    public Task<bool> TryRotateRefreshAsync(
        TokenHash expectedOldRefreshTokenHash,
        TokenHash newRefreshTokenHash,
        DateTimeOffset newExpiresAtUtc,
        CancellationToken cancellationToken = default);
}
