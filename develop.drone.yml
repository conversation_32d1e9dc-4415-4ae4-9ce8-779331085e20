kind: pipeline
type: docker
name: deploy-on-main

trigger:
  branch:
    - develop
  event:
    - push

steps:

  - name: run-tests
    image: mcr.microsoft.com/dotnet/sdk:9.0
    commands:
      - echo "Восстановление зависимостей..."
      - dotnet restore --verbosity normal
      - echo "Сборка проектов..."
      - dotnet build --configuration Release --no-restore --verbosity normal
      - echo "Запуск тестов..."
      - dotnet test --configuration Release --no-build --verbosity normal --logger "console;verbosity=detailed"
  
  - name: build-and-run
    image: docker:27-cli
    environment:
      PROJECTS_HOME:
        from_secret: PROJECTS_HOME
      APP_PORT_MAPPING: 40001
      MONGO_DB_PORT_MAPPING: 40002
      SEQ_PORT_MAPPING: 40003
      HMAC_TOKEN_HASHER_PEPPER_BASE64:
        from_secret: HMAC_TOKEN_HASHER_PEPPER_BASE64
      AUTH_SESSION_ACCESS_TOKEN_SECRET:
        from_secret: AUTH_SESSION_ACCESS_TOKEN_SECRET
      AWH_GAME_SERVER_CODE: 2
      AWH_GAME_SERVER_USER_GENERATION: 2
    commands:
      - docker compose -f compose.yaml -f drone.compose.yaml down --remove-orphans
      - docker compose -f compose.yaml -f drone.compose.yaml up -d --build

