using AwhGameServer.Domain.Exceptions;
using AwhGameServer.Domain.ValueObjects;
using AwhGameServer.Domain.ValueObjects.Ids;

namespace AwhGameServer.Domain.Tests.ValueObjects;

// Конкретные реализации для тестирования
public sealed record TestUserId(string Value) : TypedId(Value);
public sealed record TestWalletId(string Value) : TypedId(Value);
public sealed record TestProductId(string Value) : TypedId(Value);

public class TypedIdTests
{
    #region Constructor Tests

    [Fact(DisplayName = "Создание TypedId с валидным значением успешно")]
    public void Constructor_WithValidValue_CreatesSuccessfully()
    {
        // Arrange
        const string validId = "user-123";

        // Act
        var userId = new TestUserId(validId);

        // Assert
        Assert.Equal(validId, userId.Value);
    }

    [Theory(DisplayName = "Создание TypedId с null или пустым значением вызывает DomainException")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    [InlineData("\r\n")]
    [InlineData(" \t \n ")]
    public void Constructor_WithNullOrEmptyValue_ThrowsDomainException(string invalidValue)
    {
        // Act & Assert
        var exception = Assert.Throws<DomainException>(() => new TestUserId(invalidValue));
        Assert.Equal("Value cannot be null or empty", exception.Message);
    }

    [Theory(DisplayName = "Создание TypedId с различными валидными значениями")]
    [InlineData("1")]
    [InlineData("a")]
    [InlineData("user-123")]
    [InlineData("WALLET_456")]
    [InlineData("550e8400-e29b-41d4-a716-************")] // GUID
    [InlineData("12345")]
    [InlineData("very-long-identifier-with-many-characters-and-symbols_123")]
    [InlineData("Пользователь123")] // Unicode
    [InlineData("用户123")] // Chinese characters
    [InlineData("!@#$%^&*()_+-=[]{}|;:,.<>?")] // Special characters
    public void Constructor_WithValidValues_PreservesValue(string validValue)
    {
        // Act
        var userId = new TestUserId(validValue);

        // Assert
        Assert.Equal(validValue, userId.Value);
    }

    #endregion

    #region ToString Tests

    [Fact(DisplayName = "ToString возвращает значение идентификатора")]
    public void ToString_ReturnsIdValue()
    {
        // Arrange
        const string idValue = "test-id-123";
        var userId = new TestUserId(idValue);

        // Act
        var result = userId.ToString();

        // Assert
        Assert.Equal(idValue, result);
    }

    [Theory(DisplayName = "ToString работает с различными значениями")]
    [InlineData("1")]
    [InlineData("simple-id")]
    [InlineData("UPPER_CASE_ID")]
    [InlineData("mixed_Case-ID.123")]
    [InlineData("Русский-идентификатор")]
    public void ToString_WithDifferentValues_ReturnsCorrectString(string idValue)
    {
        // Act
        var userId = new TestUserId(idValue);
        var result = userId.ToString();

        // Assert
        Assert.Equal(idValue, result);
    }

    #endregion

    #region Equality Tests (Record Behavior)

    [Fact(DisplayName = "Два TypedId одного типа с одинаковыми значениями равны")]
    public void Equality_SameTypeAndValue_AreEqual()
    {
        // Arrange
        const string idValue = "test-123";
        var userId1 = new TestUserId(idValue);
        var userId2 = new TestUserId(idValue);

        // Act & Assert
        Assert.Equal(userId1, userId2);
        Assert.True(userId1 == userId2);
        Assert.False(userId1 != userId2);
        Assert.Equal(userId1.GetHashCode(), userId2.GetHashCode());
    }

    [Fact(DisplayName = "Два TypedId одного типа с разными значениями не равны")]
    public void Equality_SameTypeDifferentValues_AreNotEqual()
    {
        // Arrange
        var userId1 = new TestUserId("user-123");
        var userId2 = new TestUserId("user-456");

        // Act & Assert
        Assert.NotEqual(userId1, userId2);
        Assert.False(userId1 == userId2);
        Assert.True(userId1 != userId2);
    }

    [Fact(DisplayName = "TypedId разных типов с одинаковыми значениями не равны")]
    public void Equality_DifferentTypesEqualValues_AreNotEqual()
    {
        // Arrange
        const string idValue = "123";
        var userId = new TestUserId(idValue);
        var walletId = new TestWalletId(idValue);

        // Act & Assert
        Assert.NotEqual(userId, (object)walletId);
        Assert.False(userId.Equals(walletId));
    }

    [Fact(DisplayName = "TypedId не равен null")]
    public void Equality_WithNull_IsNotEqual()
    {
        // Arrange
        var userId = new TestUserId("test");

        // Act & Assert
        Assert.NotEqual(userId, null);
        Assert.False(userId == null);
        Assert.True(userId != null);
    }

    [Theory(DisplayName = "Equality чувствителен к регистру")]
    [InlineData("test", "TEST")]
    [InlineData("User123", "user123")]
    [InlineData("ID", "id")]
    public void Equality_IsCaseSensitive(string value1, string value2)
    {
        // Arrange
        var userId1 = new TestUserId(value1);
        var userId2 = new TestUserId(value2);

        // Act & Assert
        Assert.NotEqual(userId1, userId2);
        Assert.False(userId1 == userId2);
        Assert.True(userId1 != userId2);
    }

    #endregion

    #region Type Safety Tests

    [Fact(DisplayName = "Разные типы TypedId обеспечивают типобезопасность")]
    public void TypeSafety_DifferentTypedIdTypes_AreDistinct()
    {
        // Arrange
        const string idValue = "123";
        var userId = new TestUserId(idValue);
        var walletId = new TestWalletId(idValue);
        var productId = new TestProductId(idValue);

        // Act & Assert - проверяем, что это разные типы
        Assert.IsType<TestUserId>(userId);
        Assert.IsType<TestWalletId>(walletId);
        Assert.IsType<TestProductId>(productId);

        // Проверяем, что они не приводятся друг к другу неявно
        Assert.False(ReferenceEquals(userId, walletId));
        Assert.False(ReferenceEquals(userId, productId));
        Assert.False(ReferenceEquals(walletId, productId));
    }

    [Fact(DisplayName = "TypedId является абстрактным record")]
    public void TypedId_IsAbstractRecord()
    {
        // Act & Assert
        Assert.True(typeof(TypedId).IsAbstract);
        Assert.True(typeof(TypedId).IsClass); // record is a class
    }

    #endregion

    #region Inheritance Tests

    [Fact(DisplayName = "Наследники TypedId корректно наследуют поведение")]
    public void Inheritance_DerivedTypes_InheritCorrectBehavior()
    {
        // Arrange
        const string validId = "test-123";
        const string invalidId = "";

        // Act & Assert - валидация работает для всех наследников
        var userId = new TestUserId(validId);
        var walletId = new TestWalletId(validId);
        
        Assert.Equal(validId, userId.Value);
        Assert.Equal(validId, walletId.Value);
        
        // Проверяем, что валидация срабатывает для всех типов
        Assert.Throws<DomainException>(() => new TestUserId(invalidId));
        Assert.Throws<DomainException>(() => new TestWalletId(invalidId));
    }

    [Fact(DisplayName = "Наследники могут быть sealed")]
    public void Inheritance_DerivedTypesCanBeSealed()
    {
        // Act & Assert
        Assert.True(typeof(TestUserId).IsSealed);
        Assert.True(typeof(TestWalletId).IsSealed);
        Assert.True(typeof(TestProductId).IsSealed);
    }

    #endregion

    #region Integration Tests

    [Fact(DisplayName = "Полный жизненный цикл TypedId работает корректно")]
    public void Integration_FullLifeCycle_WorksCorrectly()
    {
        // Arrange
        const string originalId = "user-550e8400-e29b-41d4-a716-************";

        // Act - создание
        var userId = new TestUserId(originalId);
        
        // Act - использование как строки через ToString
        var stringRepresentation = userId.ToString();
        
        // Act - создание копии с тем же значением
        var userIdCopy = new TestUserId(originalId);
        
        // Act - создание другого типа с тем же значением
        var walletId = new TestWalletId(originalId);

        // Assert
        Assert.Equal(originalId, userId.Value);
        Assert.Equal(originalId, stringRepresentation);
        Assert.Equal(userId, userIdCopy);
        Assert.NotEqual(userId, (object)walletId);
        Assert.Equal(userId.Value, walletId.Value); // значения равны
        Assert.NotEqual(userId.GetType(), walletId.GetType()); // типы разные
    }

    [Fact(DisplayName = "TypedId можно использовать в коллекциях")]
    public void Integration_CanBeUsedInCollections()
    {
        // Arrange
        var userIds = new[]
        {
            new TestUserId("user-1"),
            new TestUserId("user-2"),
            new TestUserId("user-3"),
            new TestUserId("user-1") // дубликат
        };

        // Act
        var distinctUserIds = userIds.Distinct().ToArray();
        var hashSet = new HashSet<TestUserId>(userIds);

        // Assert
        Assert.Equal(3, distinctUserIds.Length); // дубликат исключён
        Assert.Equal(3, hashSet.Count); // HashSet также исключил дубликат
        Assert.Contains(new TestUserId("user-1"), hashSet);
        Assert.Contains(new TestUserId("user-2"), hashSet);
        Assert.Contains(new TestUserId("user-3"), hashSet);
    }

    [Theory(DisplayName = "TypedId корректно работает с различными форматами идентификаторов")]
    [InlineData("1")] // числовой
    [InlineData("550e8400-e29b-41d4-a716-************")] // GUID
    [InlineData("user_123_active")] // составной с underscores
    [InlineData("prefix:suffix:123")] // с разделителями
    [InlineData("CamelCaseId123")] // CamelCase
    [InlineData("kebab-case-id-456")] // kebab-case
    [InlineData("SCREAMING_SNAKE_CASE_789")] // SCREAMING_SNAKE_CASE
    public void Integration_WorksWithVariousIdFormats(string idFormat)
    {
        // Act
        var userId = new TestUserId(idFormat);
        var walletId = new TestWalletId(idFormat);

        // Assert
        Assert.Equal(idFormat, userId.Value);
        Assert.Equal(idFormat, userId.ToString());
        Assert.Equal(idFormat, walletId.Value);
        Assert.NotEqual(userId, (object)walletId); // разные типы
    }

    #endregion

    #region Value Property Tests

    [Fact(DisplayName = "Свойство Value доступно только для чтения")]
    public void ValueProperty_IsReadOnly()
    {
        // Arrange
        var userId = new TestUserId("test-123");

        // Act & Assert
        var valueProperty = typeof(TestUserId).GetProperty(nameof(TestUserId.Value));
        Assert.NotNull(valueProperty);
        Assert.True(valueProperty.CanRead);
        Assert.False(valueProperty.CanWrite); // у record с init свойство фактически только для чтения после создания
    }

    [Fact(DisplayName = "Свойство Value не может быть изменено после создания")]
    public void ValueProperty_IsImmutableAfterCreation()
    {
        // Arrange
        const string originalValue = "original-id";
        var userId = new TestUserId(originalValue);

        // Act & Assert
        Assert.Equal(originalValue, userId.Value);
        
        // Попытка изменения через рефлексию должна не удаваться или не влиять на публичное поведение
        // (record обеспечивает иммутабельность через init-only свойства)
    }

    #endregion
}