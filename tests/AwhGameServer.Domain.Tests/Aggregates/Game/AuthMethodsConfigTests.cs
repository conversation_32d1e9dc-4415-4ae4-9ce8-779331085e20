using AwhGameServer.Domain.Entities.Game;

namespace AwhGameServer.Domain.Tests.Aggregates.Game;

public class AuthMethodsConfigTests
{
    #region Constructor Tests

    [Fact(DisplayName = "Создание конфигурации с валидными методами успешно")]
    public void Constructor_WithValidMethods_CreatesSuccessfully()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true),
            new AuthMethod("OAuth2.0_Apple", false, true),
            new AuthMethod("PhoneNumber", true, false)
        };

        // Act
        var config = new AuthMethodsConfig(authMethods);

        // Assert
        Assert.Equal(3, config.AllowedAuthMethods.Count);
        Assert.Equal("OAuth2.0_Google", config.AllowedAuthMethods[0].MethodKey);
        Assert.Equal("OAuth2.0_Apple", config.AllowedAuthMethods[1].MethodKey);
        Assert.Equal("PhoneNumber", config.AllowedAuthMethods[2].MethodKey);
    }

    [Fact(DisplayName = "Создание конфигурации с пустой коллекцией успешно")]
    public void Constructor_WithEmptyCollection_CreatesSuccessfully()
    {
        // Arrange
        var emptyMethods = new List<AuthMethod>();

        // Act
        var config = new AuthMethodsConfig(emptyMethods);

        // Assert
        Assert.Empty(config.AllowedAuthMethods);
    }

    [Fact(DisplayName = "Создание конфигурации с null коллекцией вызывает исключение")]
    public void Constructor_WithNullCollection_ThrowsException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new AuthMethodsConfig(null));
    }

    [Fact(DisplayName = "AllowedAuthMethods возвращает копию списка (read-only)")]
    public void AllowedAuthMethods_ReturnsReadOnlyCollection()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("Test", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act & Assert
        Assert.IsAssignableFrom<IReadOnlyList<AuthMethod>>(config.AllowedAuthMethods);
    }

    [Fact(DisplayName = "Изменение исходной коллекции не влияет на конфигурацию")]
    public void Constructor_ModifyingOriginalCollection_DoesNotAffectConfig()
    {
        // Arrange
        var authMethodsList = new List<AuthMethod>
        {
            new("Test", true, true)
        };
        var config = new AuthMethodsConfig(authMethodsList);

        // Act
        authMethodsList.Add(new AuthMethod("Added", false, false));

        // Assert
        Assert.Single(config.AllowedAuthMethods);
        Assert.Equal("Test", config.AllowedAuthMethods[0].MethodKey);
        Assert.Single(config.AllowedAuthMethods);
    }

    #endregion

    #region IsRegistrationMethodAllowed Tests

    [Fact(DisplayName = "IsRegistrationMethodAllowed возвращает true для разрешённого метода регистрации")]
    public void IsRegistrationMethodAllowed_WithAllowedMethod_ReturnsTrue()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsRegistrationMethodAllowed("OAuth2.0_Google");

        // Assert
        Assert.True(result);
    }

    [Fact(DisplayName = "IsRegistrationMethodAllowed возвращает false для запрещённого метода регистрации")]
    public void IsRegistrationMethodAllowed_WithDisallowedMethod_ReturnsFalse()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Apple", false, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsRegistrationMethodAllowed("OAuth2.0_Apple");

        // Assert
        Assert.False(result);
    }

    [Fact(DisplayName = "IsRegistrationMethodAllowed возвращает false для несуществующего метода")]
    public void IsRegistrationMethodAllowed_WithNonExistentMethod_ReturnsFalse()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsRegistrationMethodAllowed("OAuth2.0_Facebook");

        // Assert
        Assert.False(result);
    }

    [Theory(DisplayName = "IsRegistrationMethodAllowed возвращает false для null или пустых ключей")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void IsRegistrationMethodAllowed_WithNullOrEmptyKey_ReturnsFalse(string methodKey)
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsRegistrationMethodAllowed(methodKey);

        // Assert
        Assert.False(result);
    }

    [Fact(DisplayName = "IsRegistrationMethodAllowed чувствителен к регистру")]
    public void IsRegistrationMethodAllowed_IsCaseSensitive()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var correctCase = config.IsRegistrationMethodAllowed("OAuth2.0_Google");
        var wrongCase = config.IsRegistrationMethodAllowed("oauth2.0_google");

        // Assert
        Assert.True(correctCase);
        Assert.False(wrongCase);
    }

    [Fact(DisplayName = "IsRegistrationMethodAllowed возвращает false когда логин запрещён")]
    public void IsRegistrationMethodAllowed_WhenLoginDisallowed_ReturnsFalse()
    {
        // Arrange - регистрация true, но логин false
        var authMethods = new[]
        {
            new AuthMethod("PhoneNumber", true, false)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsRegistrationMethodAllowed("PhoneNumber");

        // Assert
        Assert.False(result); // Должно быть false, т.к. запрет логина блокирует регистрацию
    }

    #endregion

    #region IsLoginMethodAllowed Tests

    [Fact(DisplayName = "IsLoginMethodAllowed возвращает true для разрешённого метода входа")]
    public void IsLoginMethodAllowed_WithAllowedMethod_ReturnsTrue()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", false, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsLoginMethodAllowed("OAuth2.0_Google");

        // Assert
        Assert.True(result);
    }

    [Fact(DisplayName = "IsLoginMethodAllowed возвращает false для запрещённого метода входа")]
    public void IsLoginMethodAllowed_WithDisallowedMethod_ReturnsFalse()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("PhoneNumber", true, false)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsLoginMethodAllowed("PhoneNumber");

        // Assert
        Assert.False(result);
    }

    [Fact(DisplayName = "IsLoginMethodAllowed возвращает false для несуществующего метода")]
    public void IsLoginMethodAllowed_WithNonExistentMethod_ReturnsFalse()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsLoginMethodAllowed("OAuth2.0_Facebook");

        // Assert
        Assert.False(result);
    }

    [Theory(DisplayName = "IsLoginMethodAllowed возвращает false для null или пустых ключей")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void IsLoginMethodAllowed_WithNullOrEmptyKey_ReturnsFalse(string methodKey)
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var result = config.IsLoginMethodAllowed(methodKey);

        // Assert
        Assert.False(result);
    }

    [Fact(DisplayName = "IsLoginMethodAllowed чувствителен к регистру")]
    public void IsLoginMethodAllowed_IsCaseSensitive()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var correctCase = config.IsLoginMethodAllowed("OAuth2.0_Google");
        var wrongCase = config.IsLoginMethodAllowed("OAUTH2.0_GOOGLE");

        // Assert
        Assert.True(correctCase);
        Assert.False(wrongCase);
    }

    #endregion

    #region Integration Tests

    [Fact(DisplayName = "Комплексный тест с несколькими методами аутентификации")]
    public void IntegrationTest_WithMultipleAuthMethods_WorksCorrectly()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("OAuth2.0_Google", true, true),      // регистрация и вход разрешены
            new AuthMethod("OAuth2.0_Apple", false, true),     // только вход разрешён
            new AuthMethod("PhoneNumber", true, false),        // ни регистрация, ни вход не разрешены (из-за login=false)
            new AuthMethod("EmailPassword", true, true)        // регистрация и вход разрешены
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act & Assert - Google
        Assert.True(config.IsLoginMethodAllowed("OAuth2.0_Google"));
        Assert.True(config.IsRegistrationMethodAllowed("OAuth2.0_Google"));

        // Act & Assert - Apple
        Assert.True(config.IsLoginMethodAllowed("OAuth2.0_Apple"));
        Assert.False(config.IsRegistrationMethodAllowed("OAuth2.0_Apple"));

        // Act & Assert - Phone
        Assert.False(config.IsLoginMethodAllowed("PhoneNumber"));
        Assert.False(config.IsRegistrationMethodAllowed("PhoneNumber"));

        // Act & Assert - Email
        Assert.True(config.IsLoginMethodAllowed("EmailPassword"));
        Assert.True(config.IsRegistrationMethodAllowed("EmailPassword"));

        // Act & Assert - Non-existent
        Assert.False(config.IsLoginMethodAllowed("OAuth2.0_Facebook"));
        Assert.False(config.IsRegistrationMethodAllowed("OAuth2.0_Facebook"));
    }

    [Fact(DisplayName = "Конфигурация с дублирующимися ключами сохраняет все записи")]
    public void Constructor_WithDuplicateKeys_PreservesAllEntries()
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("Test", true, true),
            new AuthMethod("Test", false, false)  // Дублирующийся ключ
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var loginResult = config.IsLoginMethodAllowed("Test");
        var registrationResult = config.IsRegistrationMethodAllowed("Test");

        // Assert
        Assert.Equal(2, config.AllowedAuthMethods.Count);
        // FirstOrDefault вернёт первое совпадение
        Assert.True(loginResult);
        Assert.True(registrationResult);
    }

    [Fact(DisplayName = "Пустая конфигурация возвращает false для любых методов")]
    public void EmptyConfig_ReturnsFalseForAnyMethod()
    {
        // Arrange
        var config = new AuthMethodsConfig(new List<AuthMethod>());

        // Act & Assert
        Assert.False(config.IsLoginMethodAllowed("OAuth2.0_Google"));
        Assert.False(config.IsRegistrationMethodAllowed("OAuth2.0_Google"));
        Assert.False(config.IsLoginMethodAllowed("AnyMethod"));
        Assert.False(config.IsRegistrationMethodAllowed("AnyMethod"));
    }

    [Theory(DisplayName = "Различные комбинации разрешений работают корректно")]
    [InlineData(true, true, true, true)]   // оба разрешены
    [InlineData(false, true, true, false)] // только логин
    [InlineData(true, false, false, false)] // ни один (из-за login=false)
    [InlineData(false, false, false, false)] // ни один
    public void PermissionCombinations_WorkCorrectly(
        bool registrationParam, bool loginParam, 
        bool expectedLogin, bool expectedRegistration)
    {
        // Arrange
        var authMethods = new[]
        {
            new AuthMethod("Test", registrationParam, loginParam)
        };
        var config = new AuthMethodsConfig(authMethods);

        // Act
        var actualLogin = config.IsLoginMethodAllowed("Test");
        var actualRegistration = config.IsRegistrationMethodAllowed("Test");

        // Assert
        Assert.Equal(expectedLogin, actualLogin);
        Assert.Equal(expectedRegistration, actualRegistration);
    }

    #endregion
}
