using AwhGameServer.Domain.ValueObjects.Ids.Users;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using Testcontainers.MongoDb;
using Moq;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Services;

namespace AwhGameServer.Infrastructure.Tests.Services;

/// <summary>
/// Тесты для генератора идентификаторов пользователей UserIdGenerator.
/// Проверяют корректность генерации структурированных ID в формате SGNNNNNNNN,
/// последовательность номеров и thread-safety в многопоточной среде.
/// Использует Testcontainers для изолированного тестирования с реальным MongoDB контейнером.
/// </summary>
public class UserIdGeneratorTests : IAsyncDisposable
{
    private readonly UserIdGenerator<UserId> _generator;
    private readonly IMongoClient _mongoClient;
    private readonly string _testDatabaseName;
    private readonly MongoDbContainer _mongoContainer;
    private readonly Mock<ILogger<UserIdGenerator<UserId>>> _loggerMock;

    public UserIdGeneratorTests()
    {
        _mongoContainer = new MongoDbBuilder()
            .WithImage("mongo:7.0")
            .Build();

        _mongoContainer.StartAsync().GetAwaiter().GetResult();

        _testDatabaseName = $"test_db_{Guid.NewGuid():N}";
        _mongoClient = new MongoClient(_mongoContainer.GetConnectionString());

        var config = new UserIdGeneratorConfig
        {
            ServerCode = 7,
            UserGeneration = 1,
            CounterKey = $"test_counter_{Guid.NewGuid():N}"
        };

        var usersDataDbConfig = new UsersDataDbConfig
        {
            DatabaseName = _testDatabaseName
        };

        var configOptions = Options.Create(config);
        var dbConfigOptions = Options.Create(usersDataDbConfig);
        _loggerMock = new Mock<ILogger<UserIdGenerator<UserId>>>();

        _generator = new UserIdGenerator<UserId>(configOptions, _mongoClient, dbConfigOptions, _loggerMock.Object);
    }

    [Fact(DisplayName = "Первый вызов генератора возвращает корректный формат ID")]
    public async Task New_FirstCall_ReturnsCorrectFormat()
    {
        var userId = await _generator.New();

        Assert.Equal("7100000001", userId.Value);
    }

    [Fact(DisplayName = "Множественные вызовы генератора возвращают последовательные ID")]
    public async Task New_MultipleCalls_ReturnsSequentialIds()
    {
        var userId1 = await _generator.New();
        var userId2 = await _generator.New();
        var userId3 = await _generator.New();

        Assert.Equal("7100000001", userId1.Value);
        Assert.Equal("7100000002", userId2.Value);
        Assert.Equal("7100000003", userId3.Value);
    }

    [Fact(DisplayName = "Генератор с другим кодом сервера возвращает корректный формат")]
    public async Task New_DifferentServerCode_ReturnsCorrectFormat()
    {
        var config = new UserIdGeneratorConfig
        {
            ServerCode = 3,
            UserGeneration = 2,
            CounterKey = $"test_counter_different_{Guid.NewGuid():N}"
        };

        var usersDataDbConfig = new UsersDataDbConfig
        {
            DatabaseName = _testDatabaseName
        };

        var configOptions = Options.Create(config);
        var dbConfigOptions = Options.Create(usersDataDbConfig);
        var loggerMock = new Mock<ILogger<UserIdGenerator<UserId>>>();
        var generator = new UserIdGenerator<UserId>(configOptions, _mongoClient, dbConfigOptions, loggerMock.Object);

        var userId = await generator.New();

        Assert.Equal("3200000001", userId.Value);
    }

    [Fact(DisplayName = "Параллельные вызовы генератора возвращают уникальные ID без дублирования")]
    public async Task New_ConcurrentCalls_ReturnsUniqueIds()
    {
        var config = new UserIdGeneratorConfig
        {
            ServerCode = 9,
            UserGeneration = 5,
            CounterKey = $"concurrent_test_counter_{Guid.NewGuid():N}"
        };

        var usersDataDbConfig = new UsersDataDbConfig
        {
            DatabaseName = _testDatabaseName
        };

        var configOptions = Options.Create(config);
        var dbConfigOptions = Options.Create(usersDataDbConfig);
        var loggerMock = new Mock<ILogger<UserIdGenerator<UserId>>>();
        var generator = new UserIdGenerator<UserId>(configOptions, _mongoClient, dbConfigOptions, loggerMock.Object);

        const int concurrentCallsCount = 50;
        var tasks = new List<Task<UserId>>();

        for (int i = 0; i < concurrentCallsCount; i++)
        {
            tasks.Add(generator.New());
        }

        var results = await Task.WhenAll(tasks);
        var uniqueIds = results.Select(x => x.Value).Distinct().ToList();

        Assert.Equal(concurrentCallsCount, uniqueIds.Count);
        Assert.All(results, result => Assert.StartsWith("95", result.Value));
        Assert.All(results, result => Assert.Equal(10, result.Value.Length));
    }

    public async ValueTask DisposeAsync()
    {
        _mongoClient.DropDatabase(_testDatabaseName);
        await _mongoContainer.DisposeAsync();
    }
}
