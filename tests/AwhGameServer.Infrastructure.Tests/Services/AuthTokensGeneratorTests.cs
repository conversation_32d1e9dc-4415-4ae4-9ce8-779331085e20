using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using AwhGameServer.Domain.ValueObjects.Ids.Users;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Moq;
using FluentAssertions;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Services;

namespace AwhGameServer.Infrastructure.Tests.Services;

public class AuthTokensGeneratorTests
{
    private const string ValidSecretBase64 = "dGVzdC1zZWNyZXQtZm9yLWF1dGgtdG9rZW5zLWdlbmVyYXRvci10ZXN0cy0xMjM0NTY3ODkwYWJjZGVmZ2hpams=";
    private const string TestIssuer = "test-issuer";
    private const string TestAudience = "test-audience";
    private const int TestAccessTokenLifetimeMinutes = 15;
    private const int TestRefreshTokenLifetimeDays = 30;

    [Fact(DisplayName = "GenerateAuthTokens с валидными параметрами возвращает корректные токены")]
    public async Task GenerateAuthTokens_WithValidParameters_ReturnsCorrectTokens()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        result.Should().NotBeNull();
        result.AccessToken.Should().NotBeNullOrEmpty();
        result.RefreshToken.Should().NotBeNullOrEmpty();
        result.AccessTokenExpiresAtUtc.Should().BeAfter(DateTime.UtcNow);
        result.RefreshTokenExpiresAtUtc.Should().BeAfter(DateTime.UtcNow);
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует валидный JWT access токен")]
    public async Task GenerateAuthTokens_GeneratesValidJwtAccessToken()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.ReadJwtToken(result.AccessToken);

        token.Should().NotBeNull();
        token.Issuer.Should().Be(TestIssuer);
        token.Audiences.Should().Contain(TestAudience);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sub && c.Value == userId.Value);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sid && c.Value == sessionId);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Jti);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Iat);
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует refresh токен в формате Base64Url")]
    public async Task GenerateAuthTokens_GeneratesRefreshTokenInBase64UrlFormat()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        result.RefreshToken.Should().NotBeNullOrEmpty();
        result.RefreshToken.Should().NotContain("+");
        result.RefreshToken.Should().NotContain("/");
        result.RefreshToken.Should().NotContain("=");
        result.RefreshToken.Should().MatchRegex("^[A-Za-z0-9_-]+$");
    }

    [Fact(DisplayName = "GenerateAuthTokens устанавливает корректное время истечения токенов")]
    public async Task GenerateAuthTokens_SetsCorrectTokenExpirationTimes()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var beforeGeneration = DateTime.UtcNow;

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var afterGeneration = DateTime.UtcNow;
        var expectedAccessExpiry = beforeGeneration.AddMinutes(TestAccessTokenLifetimeMinutes);
        var expectedRefreshExpiry = beforeGeneration.AddDays(TestRefreshTokenLifetimeDays);

        result.AccessTokenExpiresAtUtc.Should().BeCloseTo(expectedAccessExpiry, TimeSpan.FromSeconds(5));
        result.RefreshTokenExpiresAtUtc.Should().BeCloseTo(expectedRefreshExpiry, TimeSpan.FromSeconds(5));
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует уникальные токены при множественных вызовах")]
    public async Task GenerateAuthTokens_GeneratesUniqueTokensOnMultipleCalls()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result1 = await generator.GenerateAuthTokens(sessionId, userId);
        var result2 = await generator.GenerateAuthTokens(sessionId, userId);
        var result3 = await generator.GenerateAuthTokens(sessionId, userId);

        result1.AccessToken.Should().NotBe(result2.AccessToken);
        result1.AccessToken.Should().NotBe(result3.AccessToken);
        result2.AccessToken.Should().NotBe(result3.AccessToken);

        result1.RefreshToken.Should().NotBe(result2.RefreshToken);
        result1.RefreshToken.Should().NotBe(result3.RefreshToken);
        result2.RefreshToken.Should().NotBe(result3.RefreshToken);
    }

    [Theory(DisplayName = "GenerateAuthTokens работает с различными sessionId и userId")]
    [InlineData("session-1", "user-1")]
    [InlineData("session-550e8400-e29b-41d4-a716-446655440000", "user-550e8400-e29b-41d4-a716-446655440000")]
    [InlineData("guest-session", "guest-user")]
    [InlineData("mobile-session-123", "mobile-user-456")]
    [InlineData("web-session-abc", "web-user-def")]
    public async Task GenerateAuthTokens_WorksWithVariousSessionIdAndUserId(string sessionId, string userIdValue)
    {
        var generator = CreateGenerator();
        var userId = new UserId(userIdValue);

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        result.Should().NotBeNull();
        result.AccessToken.Should().NotBeNullOrEmpty();
        result.RefreshToken.Should().NotBeNullOrEmpty();

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.ReadJwtToken(result.AccessToken);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sub && c.Value == userIdValue);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sid && c.Value == sessionId);
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует access токен с корректной подписью")]
    public async Task GenerateAuthTokens_GeneratesAccessTokenWithCorrectSignature()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var keyBytes = Convert.FromBase64String(ValidSecretBase64);
        var key = new SymmetricSecurityKey(keyBytes);
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = TestIssuer,
            ValidateAudience = true,
            ValidAudience = TestAudience,
            ValidateLifetime = true,
            IssuerSigningKey = key,
            ValidateIssuerSigningKey = true,
            ClockSkew = TimeSpan.Zero
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var principal = tokenHandler.ValidateToken(result.AccessToken, validationParameters, out var validatedToken);

        principal.Should().NotBeNull();
        validatedToken.Should().NotBeNull();
    }

    [Fact(DisplayName = "GenerateAuthTokens с CancellationToken работает корректно")]
    public async Task GenerateAuthTokens_WithCancellationToken_WorksCorrectly()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        using var cts = new CancellationTokenSource();

        var result = await generator.GenerateAuthTokens(sessionId, userId, cts.Token);

        result.Should().NotBeNull();
        result.AccessToken.Should().NotBeNullOrEmpty();
        result.RefreshToken.Should().NotBeNullOrEmpty();
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует refresh токен ожидаемой длины")]
    public async Task GenerateAuthTokens_GeneratesRefreshTokenOfExpectedLength()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var refreshTokenBytes = Convert.FromBase64String(AddBase64Padding(result.RefreshToken.Replace('-', '+').Replace('_', '/')));
        refreshTokenBytes.Length.Should().Be(32);
    }

    [Fact(DisplayName = "GenerateAuthTokens работает стабильно при большом количестве вызовов")]
    public async Task GenerateAuthTokens_WorksStablyWithLargeNumberOfCalls()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        const int largeCallsCount = 100;
        var results = new List<string>();

        for (int i = 0; i < largeCallsCount; i++)
        {
            var result = await generator.GenerateAuthTokens(sessionId, userId);
            results.Add(result.AccessToken);
            results.Add(result.RefreshToken);
        }

        var uniqueTokens = results.Distinct().ToList();
        uniqueTokens.Count.Should().Be(largeCallsCount * 2);
    }

    private static AuthTokensGenerator CreateGenerator()
    {
        var config = new AuthSessionConfig
        {
            AccessTokenIssuer = TestIssuer,
            AccessTokenAudience = TestAudience,
            AccessTokenLifetimeMinutes = TestAccessTokenLifetimeMinutes,
            AccessTokenSecretBase64 = ValidSecretBase64,
            RefreshTokenLifetimeDays = TestRefreshTokenLifetimeDays
        };

        var options = Options.Create(config);
        var loggerMock = new Mock<ILogger<AuthTokensGenerator>>();
        return new AuthTokensGenerator(options, loggerMock.Object);
    }

    [Fact(DisplayName = "GenerateAuthTokens с пустым sessionId работает корректно")]
    public async Task GenerateAuthTokens_WithEmptySessionId_WorksCorrectly()
    {
        var generator = CreateGenerator();
        var sessionId = string.Empty;
        var userId = new UserId("user-456");

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        result.Should().NotBeNull();
        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.ReadJwtToken(result.AccessToken);
        token.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sid && c.Value == string.Empty);
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует токены с корректным временем выдачи")]
    public async Task GenerateAuthTokens_GeneratesTokensWithCorrectIssuedAt()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var beforeGeneration = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var afterGeneration = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.ReadJwtToken(result.AccessToken);

        var iatClaim = token.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Iat);
        iatClaim.Should().NotBeNull();
        var issuedAt = long.Parse(iatClaim!.Value);

        issuedAt.Should().BeGreaterThanOrEqualTo(beforeGeneration);
        issuedAt.Should().BeLessThanOrEqualTo(afterGeneration);
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует access токен с уникальным JTI")]
    public async Task GenerateAuthTokens_GeneratesAccessTokenWithUniqueJti()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var result1 = await generator.GenerateAuthTokens(sessionId, userId);
        var result2 = await generator.GenerateAuthTokens(sessionId, userId);

        var tokenHandler = new JwtSecurityTokenHandler();
        var token1 = tokenHandler.ReadJwtToken(result1.AccessToken);
        var token2 = tokenHandler.ReadJwtToken(result2.AccessToken);

        var jti1 = token1.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;
        var jti2 = token2.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;

        jti1.Should().NotBeNullOrEmpty();
        jti2.Should().NotBeNullOrEmpty();
        jti1.Should().NotBe(jti2);
        Guid.TryParse(jti1, out _).Should().BeTrue();
        Guid.TryParse(jti2, out _).Should().BeTrue();
    }

    [Fact(DisplayName = "GenerateAuthTokens генерирует access токен с корректным NotBefore")]
    public async Task GenerateAuthTokens_GeneratesAccessTokenWithCorrectNotBefore()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var beforeGeneration = DateTime.UtcNow;

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.ReadJwtToken(result.AccessToken);

        token.ValidFrom.Should().BeCloseTo(beforeGeneration, TimeSpan.FromSeconds(5));
        token.ValidTo.Should().BeCloseTo(beforeGeneration.AddMinutes(TestAccessTokenLifetimeMinutes), TimeSpan.FromSeconds(5));
    }

    [Theory(DisplayName = "GenerateAuthTokens работает с различными конфигурациями времени жизни")]
    [InlineData(5, 7)]
    [InlineData(30, 90)]
    [InlineData(60, 365)]
    [InlineData(1, 1)]
    public async Task GenerateAuthTokens_WorksWithDifferentLifetimeConfigurations(int accessMinutes, int refreshDays)
    {
        var config = new AuthSessionConfig
        {
            AccessTokenIssuer = TestIssuer,
            AccessTokenAudience = TestAudience,
            AccessTokenLifetimeMinutes = accessMinutes,
            AccessTokenSecretBase64 = ValidSecretBase64,
            RefreshTokenLifetimeDays = refreshDays
        };

        var loggerMock = new Mock<ILogger<AuthTokensGenerator>>();
        var generator = new AuthTokensGenerator(Options.Create(config), loggerMock.Object);
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var beforeGeneration = DateTime.UtcNow;

        var result = await generator.GenerateAuthTokens(sessionId, userId);

        var expectedAccessExpiry = beforeGeneration.AddMinutes(accessMinutes);
        var expectedRefreshExpiry = beforeGeneration.AddDays(refreshDays);

        result.AccessTokenExpiresAtUtc.Should().BeCloseTo(expectedAccessExpiry, TimeSpan.FromSeconds(5));
        result.RefreshTokenExpiresAtUtc.Should().BeCloseTo(expectedRefreshExpiry, TimeSpan.FromSeconds(5));
    }

    [Fact(DisplayName = "GenerateAuthTokens возвращает завершенную задачу")]
    public async Task GenerateAuthTokens_ReturnsCompletedTask()
    {
        var generator = CreateGenerator();
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");

        var task = generator.GenerateAuthTokens(sessionId, userId);

        task.IsCompleted.Should().BeTrue();
        var result = await task;
        result.Should().NotBeNull();
    }

    private static string AddBase64Padding(string base64)
    {
        var padding = 4 - (base64.Length % 4);
        if (padding == 4) return base64;
        return base64 + new string('=', padding);
    }
}
