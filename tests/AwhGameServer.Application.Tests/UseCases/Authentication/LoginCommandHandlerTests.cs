using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Application.Abstractions.Logging;
using AwhGameServer.Application.Dto;
using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Application.Exceptions;
using AwhGameServer.Application.Models;
using AwhGameServer.Domain.Entities.Game;
using AwhGameServer.Domain.Entities.Users;
using AwhGameServer.Domain.ValueObjects.Ids.Users;
using FluentAssertions;
using Moq;

namespace AwhGameServer.Application.Tests.UseCases.Authentication;

public class LoginCommandHandlerTests
{
    private readonly Mock<IAuthenticationUow> _uowMock;
    private readonly Mock<ITypedIdGenerator<UserId>> _userIdGeneratorMock;
    private readonly Mock<ITypedIdGenerator<AuthIdentityId>> _authIdentityIdGeneratorMock;
    private readonly Mock<IAuthTokensGenerator> _authTokensGeneratorMock;
    private readonly Mock<ITokenHasher> _tokenHasherMock;
    private readonly Mock<IAuthSessionStore> _authSessionStoreMock;
    private readonly Mock<IAuthMethodsConfigReadRepository> _authMethodsConfigRepoMock;
    private readonly Mock<IAuthIdentityRepository> _authIdentityRepoMock;
    private readonly Mock<IAppLogger<LoginCommandHandler>> _loggerMock;
    private readonly LoginCommandHandler _handler;

    public LoginCommandHandlerTests()
    {
        _uowMock = new Mock<IAuthenticationUow>();
        _userIdGeneratorMock = new Mock<ITypedIdGenerator<UserId>>();
        _authIdentityIdGeneratorMock = new Mock<ITypedIdGenerator<AuthIdentityId>>();
        _authTokensGeneratorMock = new Mock<IAuthTokensGenerator>();
        _tokenHasherMock = new Mock<ITokenHasher>();
        _authSessionStoreMock = new Mock<IAuthSessionStore>();
        _authMethodsConfigRepoMock = new Mock<IAuthMethodsConfigReadRepository>();
        _authIdentityRepoMock = new Mock<IAuthIdentityRepository>();
        _loggerMock = new Mock<IAppLogger<LoginCommandHandler>>();

        _uowMock.Setup(x => x.AuthMethodsConfigRepository).Returns(_authMethodsConfigRepoMock.Object);
        _uowMock.Setup(x => x.AuthIdentityRepository).Returns(_authIdentityRepoMock.Object);

        _handler = new LoginCommandHandler(
            _uowMock.Object,
            _userIdGeneratorMock.Object,
            _authIdentityIdGeneratorMock.Object,
            _authTokensGeneratorMock.Object,
            _tokenHasherMock.Object,
            _authSessionStoreMock.Object,
            _loggerMock.Object);
    }

    #region Validation Tests

    [Fact(DisplayName = "Handle с пустой коллекцией AuthIdentities выбрасывает ApplicationArgumentException")]
    public async Task Handle_WithEmptyAuthIdentities_ThrowsApplicationArgumentException()
    {
        var command = new LoginCommand(new List<AuthIdentityDto>());

        var exception = await Assert.ThrowsAsync<ApplicationArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("AuthIdentities cannot be empty (Parameter 'AuthIdentities')");
        exception.ParamName.Should().Be("AuthIdentities");
    }

    [Fact(DisplayName = "Handle с несколькими не-гостевыми точками входа выбрасывает ApplicationArgumentException")]
    public async Task Handle_WithMultipleNonGuestIdentities_ThrowsApplicationArgumentException()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token"),
            new("OAuth2.0_Apple", "apple-token")
        };
        var command = new LoginCommand(authIdentities);

        var exception = await Assert.ThrowsAsync<ApplicationArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Multiple non-guest identities are not allowed (Parameter 'AuthIdentities')");
        exception.ParamName.Should().Be("AuthIdentities");
    }

    [Fact(DisplayName = "Handle с несколькими гостевыми точками входа выбрасывает ApplicationArgumentException")]
    public async Task Handle_WithMultipleGuestIdentities_ThrowsApplicationArgumentException()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("Guest", "guest-token-1"),
            new("Guest", "guest-token-2")
        };
        var command = new LoginCommand(authIdentities);

        var exception = await Assert.ThrowsAsync<ApplicationArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Multiple guest identities are not allowed (Parameter 'AuthIdentities')");
        exception.ParamName.Should().Be("AuthIdentities");
    }

    #endregion

    #region Non-Guest Authentication Tests

    [Fact(DisplayName = "Handle с существующим не-гостевым пользователем возвращает токены")]
    public async Task Handle_WithExistingNonGuestUser_ReturnsTokens()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", true, true));
        var existingAuthIdentity = CreateAuthIdentity("auth-1", "user-1", "google-token", "OAuth2.0_Google");
        var authTokens = CreateAuthTokens();
        var tokenHash = new TokenHash("hash");

        SetupUowMocks(authMethodsConfig, existingAuthIdentity);
        SetupAuthenticationMocks(authTokens, tokenHash);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.UserId.Should().Be(existingAuthIdentity.UserId.Value);
        result.IsNewUser.Should().BeFalse();
        result.AuthSessionId.Should().NotBeNullOrEmpty();
        result.AuthSessionExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        result.AuthTokens.AccessToken.Should().Be(authTokens.AccessToken);
        result.AuthTokens.RefreshToken.Should().Be(authTokens.RefreshToken);
        result.AuthTokens.AccessTokenExpiresAtUtc.Should().Be(authTokens.AccessTokenExpiresAtUtc);
        result.AuthTokens.RefreshTokenExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        VerifyAuthenticationFlow(existingAuthIdentity, authTokens, tokenHash);
    }

    [Fact(DisplayName = "Handle с запрещенным не-гостевым методом входа выбрасывает AuthenticationException")]
    public async Task Handle_WithDisallowedNonGuestLoginMethod_ThrowsAuthenticationException()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", false, false));
        SetupUowMocks(authMethodsConfig);

        var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Login with OAuth2.0_Google is not allowed");
    }

    [Fact(DisplayName = "Handle с новым не-гостевым пользователем создает нового пользователя")]
    public async Task Handle_WithNewNonGuestUser_CreatesNewUser()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", true, true));
        var newUserId = new UserId("new-user-1");
        var newAuthIdentityId = new AuthIdentityId("new-auth-1");
        var authTokens = CreateAuthTokens();
        var tokenHash = new TokenHash("hash");

        SetupUowMocks(authMethodsConfig, null);
        SetupIdGenerators(newUserId, newAuthIdentityId);
        SetupAuthenticationMocks(authTokens, tokenHash);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.UserId.Should().Be(newUserId.Value);
        result.IsNewUser.Should().BeTrue();
        result.AuthSessionId.Should().NotBeNullOrEmpty();
        result.AuthSessionExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        result.AuthTokens.AccessToken.Should().Be(authTokens.AccessToken);
        result.AuthTokens.RefreshToken.Should().Be(authTokens.RefreshToken);
        result.AuthTokens.AccessTokenExpiresAtUtc.Should().Be(authTokens.AccessTokenExpiresAtUtc);
        result.AuthTokens.RefreshTokenExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        VerifyNewUserCreation(newUserId, newAuthIdentityId, authIdentities[0]);
        VerifyAuthenticationFlowForNewUser(authTokens, tokenHash);
    }

    [Fact(DisplayName = "Handle с не-гостевой точкой входа и существующей гостевой привязывает аккаунты")]
    public async Task Handle_WithNonGuestAndExistingGuest_LinksAccounts()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token"),
            new("Guest", "guest-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(
            ("OAuth2.0_Google", true, true),
            ("Guest", true, true));

        var existingGuestIdentity = CreateAuthIdentity("guest-auth-1", "user-1", "guest-token", "Guest");
        var newAuthIdentityId = new AuthIdentityId("new-auth-1");
        var authTokens = CreateAuthTokens();
        var tokenHash = new TokenHash("hash");

        _authMethodsConfigRepoMock
            .Setup(x => x.GetConfigAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(authMethodsConfig);

        _authIdentityRepoMock
            .SetupSequence(x => x.GetByAuthMethodAndTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuthIdentity?)null)
            .ReturnsAsync(existingGuestIdentity);

        _authIdentityIdGeneratorMock
            .Setup(x => x.New())
            .ReturnsAsync(newAuthIdentityId);

        SetupAuthenticationMocks(authTokens, tokenHash);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.UserId.Should().Be(existingGuestIdentity.UserId.Value);
        result.IsNewUser.Should().BeFalse();
        result.AuthSessionId.Should().NotBeNullOrEmpty();
        result.AuthSessionExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        result.AuthTokens.AccessToken.Should().Be(authTokens.AccessToken);
        result.AuthTokens.RefreshToken.Should().Be(authTokens.RefreshToken);
        result.AuthTokens.AccessTokenExpiresAtUtc.Should().Be(authTokens.AccessTokenExpiresAtUtc);
        result.AuthTokens.RefreshTokenExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);

        _authIdentityRepoMock.Verify(
            x => x.AddAsync(
                It.Is<AuthIdentity>(ai =>
                    ai.Id == newAuthIdentityId &&
                    ai.UserId == existingGuestIdentity.UserId &&
                    ai.AuthToken == "google-token" &&
                    ai.AuthMethod == "OAuth2.0_Google"),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Handle с запрещенным методом регистрации выбрасывает AuthenticationException")]
    public async Task Handle_WithDisallowedRegistrationMethod_ThrowsAuthenticationException()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", false, false));
        SetupUowMocks(authMethodsConfig, null);

        var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Login with OAuth2.0_Google is not allowed");
    }

    [Fact(DisplayName = "Handle с разрешенным входом но запрещенной регистрацией выбрасывает AuthenticationException")]
    public async Task Handle_WithAllowedLoginButDisallowedRegistration_ThrowsAuthenticationException()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("OAuth2.0_Google", "google-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", true, false));

        // Проверим, что конфигурация правильная
        authMethodsConfig.IsLoginMethodAllowed("OAuth2.0_Google").Should().BeTrue();
        authMethodsConfig.IsRegistrationMethodAllowed("OAuth2.0_Google").Should().BeFalse();
        var newUserId = new UserId("new-user-1");
        var newAuthIdentityId = new AuthIdentityId("new-auth-1");

        _authMethodsConfigRepoMock
            .Setup(x => x.GetConfigAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(authMethodsConfig);

        _authIdentityRepoMock
            .Setup(x => x.GetByAuthMethodAndTokenAsync("OAuth2.0_Google", "google-token", It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuthIdentity?)null);

        SetupIdGenerators(newUserId, newAuthIdentityId);

        var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Registration with OAuth2.0_Google is not allowed");
    }

    #endregion

    #region Guest Authentication Tests

    [Fact(DisplayName = "Handle с существующим гостем возвращает токены")]
    public async Task Handle_WithExistingGuest_ReturnsTokens()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("Guest", "guest-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("Guest", true, true));
        var existingAuthIdentity = CreateAuthIdentity("auth-1", "user-1", "guest-token", "Guest");
        var authTokens = CreateAuthTokens();
        var tokenHash = new TokenHash("hash");

        SetupUowMocks(authMethodsConfig, existingAuthIdentity);
        SetupAuthenticationMocks(authTokens, tokenHash);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.UserId.Should().Be(existingAuthIdentity.UserId.Value);
        result.IsNewUser.Should().BeFalse();
        result.AuthSessionId.Should().NotBeNullOrEmpty();
        result.AuthSessionExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        result.AuthTokens.AccessToken.Should().Be(authTokens.AccessToken);
        result.AuthTokens.RefreshToken.Should().Be(authTokens.RefreshToken);
        result.AuthTokens.AccessTokenExpiresAtUtc.Should().Be(authTokens.AccessTokenExpiresAtUtc);
        result.AuthTokens.RefreshTokenExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        VerifyAuthenticationFlow(existingAuthIdentity, authTokens, tokenHash);
    }

    [Fact(DisplayName = "Handle с новым гостем создает нового пользователя")]
    public async Task Handle_WithNewGuest_CreatesNewUser()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("Guest", "guest-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("Guest", true, true));
        var newUserId = new UserId("new-user-1");
        var newAuthIdentityId = new AuthIdentityId("new-auth-1");
        var authTokens = CreateAuthTokens();
        var tokenHash = new TokenHash("hash");

        SetupUowMocks(authMethodsConfig, null);
        SetupIdGenerators(newUserId, newAuthIdentityId);
        SetupAuthenticationMocks(authTokens, tokenHash);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.UserId.Should().Be(newUserId.Value);
        result.IsNewUser.Should().BeTrue();
        result.AuthSessionId.Should().NotBeNullOrEmpty();
        result.AuthSessionExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        result.AuthTokens.AccessToken.Should().Be(authTokens.AccessToken);
        result.AuthTokens.RefreshToken.Should().Be(authTokens.RefreshToken);
        result.AuthTokens.AccessTokenExpiresAtUtc.Should().Be(authTokens.AccessTokenExpiresAtUtc);
        result.AuthTokens.RefreshTokenExpiresAtUtc.Should().Be(authTokens.RefreshTokenExpiresAtUtc);
        VerifyNewUserCreation(newUserId, newAuthIdentityId, authIdentities[0]);
        VerifyAuthenticationFlowForNewUser(authTokens, tokenHash);
    }

    [Fact(DisplayName = "Handle с запрещенным гостевым методом выбрасывает AuthenticationException")]
    public async Task Handle_WithDisallowedGuestMethod_ThrowsAuthenticationException()
    {
        var authIdentities = new List<AuthIdentityDto>
        {
            new("Guest", "guest-token")
        };
        var command = new LoginCommand(authIdentities);

        var authMethodsConfig = CreateAuthMethodsConfig(("Guest", false, false));
        SetupUowMocks(authMethodsConfig);

        var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Login with Guest is not allowed");
    }

    #endregion

    #region Helper Methods

    private static AuthMethodsConfig CreateAuthMethodsConfig(params (string methodKey, bool isLoginAllowed, bool isRegistrationAllowed)[] methods)
    {
        var authMethods = methods.Select(m => new AuthMethod(m.methodKey, m.isRegistrationAllowed, m.isLoginAllowed));
        return new AuthMethodsConfig(authMethods);
    }

    private static AuthIdentity CreateAuthIdentity(string authIdentityId, string userId, string authToken, string authMethod)
    {
        return new AuthIdentity(
            new AuthIdentityId(authIdentityId),
            new UserId(userId),
            authToken,
            authMethod);
    }

    private static AuthTokens CreateAuthTokens()
    {
        return new AuthTokens(
            "access-token",
            "refresh-token",
            DateTime.UtcNow.AddMinutes(15),
            DateTime.UtcNow.AddDays(30));
    }

    private void SetupUowMocks(AuthMethodsConfig authMethodsConfig, AuthIdentity? authIdentity = null)
    {
        _authMethodsConfigRepoMock
            .Setup(x => x.GetConfigAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(authMethodsConfig);

        _authIdentityRepoMock
            .Setup(x => x.GetByAuthMethodAndTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(authIdentity);
    }

    private void SetupIdGenerators(UserId userId, AuthIdentityId authIdentityId)
    {
        _userIdGeneratorMock
            .Setup(x => x.New())
            .ReturnsAsync(userId);

        _authIdentityIdGeneratorMock
            .Setup(x => x.New())
            .ReturnsAsync(authIdentityId);
    }

    private void SetupAuthenticationMocks(AuthTokens authTokens, TokenHash tokenHash)
    {
        _authTokensGeneratorMock
            .Setup(x => x.GenerateAuthTokens(It.IsAny<string>(), It.IsAny<UserId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(authTokens);

        _tokenHasherMock
            .Setup(x => x.HashToken(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(tokenHash);
    }

    #endregion

    #region Verification Methods

    private void VerifyAuthenticationFlow(AuthIdentity authIdentity, AuthTokens authTokens, TokenHash tokenHash)
    {
        _authTokensGeneratorMock.Verify(
            x => x.GenerateAuthTokens(It.IsAny<string>(), authIdentity.UserId, It.IsAny<CancellationToken>()),
            Times.Once);

        _tokenHasherMock.Verify(
            x => x.HashToken(authTokens.RefreshToken, It.IsAny<CancellationToken>()),
            Times.Once);

        _authSessionStoreMock.Verify(
            x => x.RevokeAllUserSessionsThenCreateAsync(
                It.IsAny<string>(),
                authIdentity.UserId,
                tokenHash,
                authIdentity.Id,
                authTokens.RefreshTokenExpiresAtUtc,
                It.IsAny<CancellationToken>()),
            Times.Once);

        _uowMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    private void VerifyAuthenticationFlowForNewUser(AuthTokens authTokens, TokenHash tokenHash)
    {
        _authTokensGeneratorMock.Verify(
            x => x.GenerateAuthTokens(It.IsAny<string>(), It.IsAny<UserId>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _tokenHasherMock.Verify(
            x => x.HashToken(authTokens.RefreshToken, It.IsAny<CancellationToken>()),
            Times.Once);

        _authSessionStoreMock.Verify(
            x => x.RevokeAllUserSessionsThenCreateAsync(
                It.IsAny<string>(),
                It.IsAny<UserId>(),
                tokenHash,
                It.IsAny<AuthIdentityId>(),
                authTokens.RefreshTokenExpiresAtUtc,
                It.IsAny<CancellationToken>()),
            Times.Once);

        _uowMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    private void VerifyNewUserCreation(UserId userId, AuthIdentityId authIdentityId, AuthIdentityDto authIdentityDto)
    {
        _userIdGeneratorMock.Verify(x => x.New(), Times.Once);
        _authIdentityIdGeneratorMock.Verify(x => x.New(), Times.Once);

        _authIdentityRepoMock.Verify(
            x => x.AddAsync(
                It.Is<AuthIdentity>(ai =>
                    ai.Id == authIdentityId &&
                    ai.UserId == userId &&
                    ai.AuthToken == authIdentityDto.AuthToken &&
                    ai.AuthMethod == authIdentityDto.AuthMethod),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    #endregion
}
