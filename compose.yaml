name: AwhGameServer

services:

  AwhGameServer-WebApi:
    image: awhgameserver.webapi
    build:
      context: .
      dockerfile: src/AwhGameServer.WebApi/Dockerfile
    restart: unless-stopped
    ports:
      - ${APP_PORT_MAPPING}:8080
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - MONGO_DB_CONNECTION_STRING=mongodb://AwhGameServer-MongoDb:27017/?replicaSet=rs0
      - SEQ_SERVER_URL=http://AwhGameServer-Seq:5341
      - HMAC_TOKEN_HASHER_PEPPER_BASE64=${HMAC_TOKEN_HASHER_PEPPER_BASE64}
      - AUTH_SESSION_ACCESS_TOKEN_SECRET=${AUTH_SESSION_ACCESS_TOKEN_SECRET}
      - AWH_GAME_SERVER_CODE=${AWH_GAME_SERVER_CODE}
      - AWH_GAME_SERVER_USER_GENERATION=${AWH_GAME_SERVER_USER_GENERATION}
    depends_on:
      AwhGameServer-MongoDb:
        condition: service_healthy

  AwhGameServer-MongoDb:
    image: mongo:latest
    restart: unless-stopped
    ports:
      - ${MONGO_DB_PORT_MAPPING}:27017
    volumes:
      - ./volumes/MongoDb/data:/data/db
    command: >
      bash -lc '
        set -e
        mongod --replSet rs0 --bind_ip_all &
        pid=$!
        # Ждём подъёма сервера
        for i in {1..60}; do
          mongosh --quiet --eval "db.adminCommand({ ping: 1 })" && break || sleep 1
        done
        # Инициализируем ReplicaSet, если он ещё не инициализирован
        mongosh --quiet --eval "
          try {
            rs.status()
          } catch (e) {
            rs.initiate({
              _id: \"rs0\",
              members: [{ _id: 0, host: \"AwhGameServer-MongoDb:27017\" }]
            })
          }
        "
        wait $pid
      '
    healthcheck:
      test: [ "CMD-SHELL", "mongosh --quiet --eval \"db.hello().isWritablePrimary\" | grep true" ]
      interval: 5s
      timeout: 5s
      retries: 24
      start_period: 120s

  AwhGameServer-Seq:
    image: datalust/seq:latest
    restart: unless-stopped
    ports:
      - ${SEQ_PORT_MAPPING}:80
    volumes:
      - ./volumes/Seq/data:/data
    environment:
      - ACCEPT_EULA=Y
      - SEQ_FIRSTRUN_NOAUTHENTICATION=true
